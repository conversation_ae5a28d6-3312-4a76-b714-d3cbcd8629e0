<UserControl x:Class="NAVI.UserPermissionControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 开发中页面样式 -->
        <Style x:Key="DevelopmentTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#FF495057"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="DevelopmentMessageStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="#FF6C757D"/>
            <Setter Property="TextAlignment" Value="Left"/>
            <Setter Property="LineHeight" Value="24"/>
        </Style>

        <Style x:Key="DevelopmentUnderlineStyle" TargetType="Border">
            <Setter Property="Height" Value="3"/>
            <Setter Property="Background" Value="#FF007BFF"/>
            <Setter Property="Margin" Value="0,0,0,30"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Width" Value="200"/>
        </Style>
    </UserControl.Resources>

    <!-- 采用与其他页面一致的布局结构 -->
    <Grid Background="#FFF8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部标题区域 -->
        <Border Grid.Row="0"
               Background="White"
               BorderBrush="#FFE0E0E0"
               BorderThickness="0,0,0,1"
               Padding="24,20">
            <StackPanel>
                <!-- 页面标题 -->
                <TextBlock Text="userPermission"
                          Style="{StaticResource DevelopmentTitleStyle}"/>

                <!-- 标题下划线 -->
                <Border Style="{StaticResource DevelopmentUnderlineStyle}"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 - 铺满剩余空间 -->
        <Border Grid.Row="1"
               Background="White"
               Margin="24,16,24,24"
               CornerRadius="8"
               BorderBrush="#FFE0E0E0"
               BorderThickness="1">
            <Grid>
                <!-- 居中显示开发中消息 -->
                <StackPanel VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Margin="40">
                    <!-- 开发中图标 -->
                    <Viewbox Width="80" Height="80" Margin="0,0,0,30">
                        <Canvas Width="24" Height="24">
                            <Path Fill="#FF6C757D"
                                  Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                        </Canvas>
                    </Viewbox>

                    <!-- 开发中消息 -->
                    <TextBlock Text="この機能は開発中です。"
                              Style="{StaticResource DevelopmentMessageStyle}"
                              TextAlignment="Center"
                              Margin="0,0,0,20"/>

                    <!-- 附加说明 -->
                    <TextBlock Text="ユーザー権限設定機能は現在開発中です。&#x0a;しばらくお待ちください。"
                              FontSize="14"
                              Foreground="#FF9E9E9E"
                              TextAlignment="Center"
                              LineHeight="20"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
